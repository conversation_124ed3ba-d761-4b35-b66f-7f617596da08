# 需求 [clean_failed_deletes]

## 反馈

在做1200迁移的时候，由如下报错：2025-08-21T11:59:31.831-04:00 level=ERROR msg=Failed to delete file, file="main.go:519", error=remove /mnt/ca6m0/imgs/MLS/TRB/1200/fe628/E11892153_CLMQuS.jpg: no such file or directory, operation="existing files deletion"


## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-08-11

## 原因
1. 具体查看路径，发现只有之前后缀错误的文件没有被删除。

## 解决办法
1. 写shell，根据log中的错误信息，获取到需要删除的文件 如/mnt/ca6m0/imgs/MLS/TRB/1200/fe628/E11892153* 


## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-25

## online-step
1. chmod +x ./clean_failed_deletes.sh
./clean_failed_deletes.sh /home/<USER>/rmconfig/logs/batch_migrate_trbPic-2025082*.gz --dryrun > clean_failed_deletes.log


