#!/usr/bin/env bash
# Parse "Failed to delete file" entries from logs and delete by prefix (e.g., E11892153*)
# Usage:
#   ./clean_failed_deletes.sh <log1.gz> [log2.gz ...] [--dryrun]
# Notes:
#   - Default: perform deletion. Add --dryrun to only list files to delete.
#   - Safety: only operates under /mnt/md0/imgs/MLS/TRB/1200 and /mnt/ca6m0/imgs/MLS/TRB/1200.
#   - Prefix rule: use basename before first underscore; if no underscore, use name before extension.

set -euo pipefail

if [ $# -lt 1 ]; then
  echo "Usage: $0 <log1.gz> [log2.gz ...] [--dryrun]"
  exit 1
fi

APPLY=1
declare -a LOGS=()
for arg in "$@"; do
  if [[ "$arg" == "--dryrun" || "$arg" == "dryrun" ]]; then
    APPLY=0
  else
    LOGS+=("$arg")
  fi
done

if [ ${#LOGS[@]} -eq 0 ]; then
  echo "No log files provided."
  exit 1
fi

tmp_paths="$(mktemp)"
trap 'rm -f "$tmp_paths"' EXIT

# Extract unique file paths from logs
# Pattern example in log:
#   ... Failed to delete file, ... error=remove /mnt/.../E11892153_CLMQuS.jpg: no such file or directory ...
extract_paths() {
  sed -n 's/.*error=remove \([^:]*\): no such file or directory.*/\1/p'
}

for f in "${LOGS[@]}"; do
  if [[ "$f" == *.gz ]]; then
    # gzip -cd is more portable than zcat
    gzip -cd -- "$f" | grep 'Failed to delete file' | extract_paths || true
  else
    grep 'Failed to delete file' -- "$f" | extract_paths || true
  fi
done | sort -u > "$tmp_paths"

if [ ! -s "$tmp_paths" ]; then
  echo "No matching 'Failed to delete file' entries found."
  exit 0
fi

echo "Found unique error file paths: $(wc -l < "$tmp_paths")"
echo "Candidates from logs (unique):"
cat "$tmp_paths"

# Plan deletions and (dry-run) list what will be deleted
to_delete_count=0
deleted_count=0
declare -A SEEN=()

while IFS= read -r fullpath; do
  # Safety: only under specified TRB 1200 roots
  if [[ "$fullpath" != /mnt/md0/imgs/MLS/TRB/1200/* && "$fullpath" != /mnt/ca6m0/imgs/MLS/TRB/1200/* ]]; then
    echo "Skip (unsafe path): $fullpath"
    continue
  fi

  dir="$(dirname -- "$fullpath")"
  base="$(basename -- "$fullpath")"

  # Determine the deletion prefix:
  # 1) substring before first underscore
  # 2) if no underscore, then name without extension
  stem="${base%%_*}"
  if [[ "$stem" == "$base" ]]; then
    stem="${base%.*}"
  fi

  # Basic guard
  if [[ -z "$stem" || -z "$dir" ]]; then
    echo "Skip (invalid parsed dir/stem) from: $fullpath"
    continue
  fi

  # De-duplicate by directory + prefix (stem)
  key="$dir|$stem"
  if [[ -n "${SEEN[$key]+x}" ]]; then
    continue
  fi
  SEEN[$key]=1

  # List files matching prefix in that directory
  if (( APPLY == 0 )); then
    # Dry-run: print what would be deleted
    found=$(find "$dir" -maxdepth 1 -type f -name "${stem}*" -print 2>/dev/null | wc -l || true)
    if (( found > 0 )); then
      echo "Would delete in $dir: prefix '${stem}*'"
      find "$dir" -maxdepth 1 -type f -name "${stem}*" -print 2>/dev/null
      to_delete_count=$((to_delete_count + found))
    fi
  else
    # Apply: delete files
    # Print what we delete for visibility
    found_list=$(find "$dir" -maxdepth 1 -type f -name "${stem}*" -print 2>/dev/null || true)
    if [[ -n "$found_list" ]]; then
      echo "Deleting in $dir: prefix '${stem}*'"
      echo "$found_list"
      # -delete is POSIX find extension; fallback to -exec if needed
      # Using -exec here to be broadly compatible
      find "$dir" -maxdepth 1 -type f -name "${stem}*" -exec rm -f -- {} + 2>/dev/null
      # Recount
      deln=$(printf "%s\n" "$found_list" | wc -l)
      deleted_count=$((deleted_count + deln))
    fi
  fi
done < "$tmp_paths"

if (( APPLY == 0 )); then
  echo "Dry run complete. Total files that would be deleted: $to_delete_count"
  echo "Run without --dryrun to actually delete."
else
  echo "Deletion complete. Total files deleted: $deleted_count"
fi