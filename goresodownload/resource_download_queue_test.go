package goresodownload

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/real-rm/gohelper"
	"github.com/real-rm/gomongo"
)

// setupQueueTest sets up the test environment for queue tests
func setupQueueTest(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Skip("Failed to get current directory:", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Skip("Failed to get absolute path:", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Skip("Failed to setup test environment:", err)
	}

	// Create test collection
	testCol := gomongo.Coll("rni", "reso_download_queue")
	if testCol == nil {
		t.Skip("Failed to create test collection - MongoDB not available")
	}

	// Return cleanup function
	cleanup := func(coll *gomongo.MongoCollection) {
		// Clean up test collection
		if coll != nil {
			if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
				t.Errorf("Failed to cleanup test collection: %v", err)
			}
		}
	}

	return testCol, cleanup
}

func TestResourceDownloadQueue(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	testCol, cleanup := setupQueueTest(t)
	defer cleanup(testCol)

	// Create queue instance
	queue, err := NewResourceDownloadQueue(testCol)
	require.NoError(t, err)
	require.NotNil(t, queue)

	t.Run("AddToQueue", func(t *testing.T) {
		// Create test data
		objID := primitive.NewObjectID()

		// Add to queue with new API
		err := queue.AddToQueue(objID.Hex(), 1000, "TRB", "/path/to/photo")
		assert.NoError(t, err)

		// Verify item was added
		ctx := context.Background()
		var result QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID.Hex()}).Decode(&result)
		assert.NoError(t, err)
		assert.Equal(t, objID.Hex(), result.ID)
		assert.Equal(t, 1000, result.Priority)
		assert.Equal(t, "TRB", result.Src)
		assert.Equal(t, "/path/to/photo", result.PhoP)
		// Verify initial retryCount is 0
		assert.Equal(t, 0, result.RetryCount)
	})

	t.Run("GetNextBatch", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add multiple items to queue
		for i := 0; i < 5; i++ {
			objID := primitive.NewObjectID()
			err := queue.AddToQueue(objID.Hex(), 1000, "TRB", "/path/to/photo")
			require.NoError(t, err)
		}

		// Verify items were added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Check what's actually in the database before GetNextBatch
		var sampleItem QueueItem
		err = testCol.FindOne(ctx, bson.M{}).Decode(&sampleItem)
		require.NoError(t, err)
		t.Logf("Sample item dlShallEndTs: %v, current time: %v", sampleItem.DlShallEndTs, time.Now())

		// Get first batch
		batch1, err := queue.GetNextBatch("TRB", 3)
		assert.NoError(t, err)
		t.Logf("First batch size: %d", len(batch1))
		assert.LessOrEqual(t, len(batch1), 3)
		assert.Greater(t, len(batch1), 0)

		// Verify all items have updated dlShallEndTs (should be recent, not 1970)
		for _, item := range batch1 {
			assert.True(t, item.DlShallEndTs.After(time.Now().Add(-time.Minute)),
				"GetNextBatch should return items with updated dlShallEndTs")
		}

		// Get second batch - should return remaining items (if any)
		batch2, err := queue.GetNextBatch("TRB", 3)
		assert.NoError(t, err)
		t.Logf("Second batch size: %d", len(batch2))

		// Total items returned should not exceed 5 (the number we added)
		totalReturned := len(batch1) + len(batch2)
		assert.LessOrEqual(t, totalReturned, 5, "Total items returned should not exceed items added")

		// If we got all 5 items in the first two calls, third call should return nil
		if totalReturned == 5 {
			batch3, err := queue.GetNextBatch("TRB", 3)
			assert.NoError(t, err)
			assert.Nil(t, batch3, "Third call should return nil since all items are locked")
		}
	})

	t.Run("RemoveBatchFromQueue", func(t *testing.T) {
		// Add items to queue
		var items []QueueItem
		for i := 0; i < 3; i++ {
			objID := primitive.NewObjectID()
			err := queue.AddToQueue(objID.Hex(), 1000, "TRB", "/path/to/photo")
			require.NoError(t, err)

			items = append(items, QueueItem{Src: "TRB", ID: objID.Hex()})
		}

		// Remove batch
		err := queue.RemoveBatchFromQueue(items)
		assert.NoError(t, err)

		// Verify items were removed
		ctx := context.Background()
		for _, item := range items {
			var result QueueItem
			err := testCol.FindOne(ctx, bson.M{"_id": item.ID}).Decode(&result)
			assert.Error(t, err) // Should not find the document
		}
	})

	t.Run("GetNext", func(t *testing.T) {
		// Clean up first
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add item to queue
		objID := primitive.NewObjectID()
		err := queue.AddToQueue(objID.Hex(), 1000, "TRB", "/path/to/photo")
		require.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		t.Logf("Documents in collection: %d", count)

		// Check what's actually in the database BEFORE calling GetNext
		var storedItem QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": objID.Hex()}).Decode(&storedItem)
		require.NoError(t, err)
		t.Logf("Stored item dlShallEndTs: %v, current time: %v", storedItem.DlShallEndTs, time.Now())

		// Verify the stored item has the expected default timestamp (1970)
		expectedTime, _ := time.Parse(time.RFC3339, "1970-01-01T00:00:00Z")
		assert.True(t, storedItem.DlShallEndTs.Equal(expectedTime),
			"Stored item should have default timestamp from 1970")

		// Get next item
		item, err := queue.GetNext("TRB")
		assert.NoError(t, err)
		if assert.NotNil(t, item) {
			assert.Equal(t, objID.Hex(), item.ID)
			assert.Equal(t, "TRB", item.Src)
			// GetNext should return the updated item with new dlShallEndTs
			assert.True(t, item.DlShallEndTs.After(time.Now().Add(-time.Minute)),
				"GetNext should return item with updated dlShallEndTs")
		}
	})

	t.Run("RemoveFromQueue", func(t *testing.T) {
		// Clean up
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add item to queue
		objID := primitive.NewObjectID()
		err := queue.AddToQueue(objID.Hex(), 1000, "TRB", "/path/to/photo")
		require.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(1), count)

		// Create QueueItem for removal
		item := &QueueItem{
			ID:  objID.Hex(),
			Src: "TRB",
		}

		// Remove item from queue
		err = queue.RemoveFromQueue(item)
		assert.NoError(t, err)

		// Verify item was removed
		count, err = testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	t.Run("GetNextBatch_EdgeCases", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Test with batchSize <= 0 (should default to 100)
		batch, err := queue.GetNextBatch("TRB", 0)
		assert.NoError(t, err)
		assert.Nil(t, batch)

		batch, err = queue.GetNextBatch("TRB", -5)
		assert.NoError(t, err)
		assert.Nil(t, batch)
	})

	t.Run("AddToQueue_EdgeCases", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Test with delete operation (no fullDocument)
		err := queue.AddToQueue("test-id", 500, "TEST", "/path/to/photo")
		assert.NoError(t, err)

		// Verify item was added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(1), count)
	})

	t.Run("EmptyQueue", func(t *testing.T) {
		// Clean queue
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Try to get from empty queue
		batch, err := queue.GetNextBatch("TRB", 10)
		assert.NoError(t, err)
		assert.Nil(t, batch)

		item, err := queue.GetNext("TRB")
		assert.NoError(t, err)
		assert.Nil(t, item)
	})
}

// TestResourceDownloadQueue_UnitTests tests the queue logic without MongoDB dependency
func TestResourceDownloadQueue_UnitTests(t *testing.T) {
	t.Run("NewResourceDownloadQueue_NilCollection", func(t *testing.T) {
		queue, err := NewResourceDownloadQueue(nil)
		assert.Error(t, err)
		assert.Nil(t, queue)
		assert.Contains(t, err.Error(), "collection cannot be nil")
	})

	t.Run("QueueItem_Structure", func(t *testing.T) {
		// Test QueueItem structure
		item := QueueItem{
			ID:           "test123",
			Src:          "TRB",
			Mt:           time.Now(),
			DlShallEndTs: time.Now().Add(30 * time.Second),
			Priority:     1000,
		}

		assert.Equal(t, "TRB", item.Src)
		assert.Equal(t, "test123", item.ID)
		assert.Equal(t, 1000, item.Priority)
	})

	t.Run("Constants", func(t *testing.T) {
		// Test constants are defined correctly
		assert.Equal(t, 900000, DOWNLOAD_ALLOWED_MS)
		assert.True(t, DOWNLOAD_ALLOWED_MS > 0)
	})
}

func TestResourceDownloadQueue_ComprehensiveCoverage(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	testCol, cleanup := setupQueueTest(t)
	defer cleanup(testCol)

	// Create queue instance
	queue, err := NewResourceDownloadQueue(testCol)
	require.NoError(t, err)
	require.NotNil(t, queue)

	t.Run("EnsureIndexes_Success", func(t *testing.T) {
		// Test that ensureIndexes works correctly
		err := queue.ensureIndexes()
		assert.NoError(t, err)
	})

	// New tests for retry scheduling/backoff
	t.Run("ScheduleRetry_IncrementsAndSchedules", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		id := "retry_test_item"
		require.NoError(t, queue.AddToQueue(id, 1000, "TRB", "/path/to/photo"))

		// First retry → +5 min
		var item QueueItem
		require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&item))
		before := time.Now()
		require.NoError(t, queue.ScheduleRetry(&item))

		var updated QueueItem
		require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&updated))
		assert.Equal(t, 1, updated.RetryCount)
		// Expect approximately +5 minutes (allowing wide clock jitter)
		delta := updated.DlShallEndTs.Sub(before)
		assert.GreaterOrEqual(t, int64(delta), int64(4*time.Minute), "dlShallEndTs should be at least +4 minutes")
		assert.LessOrEqual(t, int64(delta), int64(12*time.Minute), "dlShallEndTs should be within +12 minutes window")

		// Second retry → +10 min
		require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&item))
		before = time.Now()
		require.NoError(t, queue.ScheduleRetry(&item))
		require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&updated))
		assert.Equal(t, 2, updated.RetryCount)
		delta = updated.DlShallEndTs.Sub(before)
		assert.GreaterOrEqual(t, int64(delta), int64(9*time.Minute), "dlShallEndTs should be at least +9 minutes")
		assert.LessOrEqual(t, int64(delta), int64(20*time.Minute), "dlShallEndTs should be within +20 minutes window")
	})

	t.Run("ScheduleRetry_ExhaustionRemoves", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		id := "retry_remove_item"
		require.NoError(t, queue.AddToQueue(id, 1000, "TRB", "/path/to/photo"))

		// Apply schedule len(RetryBackoffSchedule) times to reach retryCount == len(schedule)
		for i := 0; i < len(RetryBackoffSchedule); i++ {
			var item QueueItem
			require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&item))
			require.NoError(t, queue.ScheduleRetry(&item))
		}

		// Now retryCount in DB should be >= len(schedule); next ScheduleRetry should remove it
		var item QueueItem
		require.NoError(t, testCol.FindOne(ctx, bson.M{"_id": id}).Decode(&item))
		require.GreaterOrEqual(t, item.RetryCount, len(RetryBackoffSchedule))
		require.NoError(t, queue.ScheduleRetry(&item))

		// Verify removed
		count, err := testCol.CountDocuments(ctx, bson.M{"_id": id})
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	t.Run("AddToQueue_MultipleBoards", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add items for different boards
		boards := []string{"TRB", "CAR", "BRE", "DDF"}
		for i, board := range boards {
			id := fmt.Sprintf("item%d", i)
			err := queue.AddToQueue(id, 1000+i*100, board, "/path/to/photo")
			assert.NoError(t, err)
		}

		// Verify all items were added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(4), count)
	})

	t.Run("GetNextBatch_DifferentBoards", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add fresh items for different boards
		boards := []string{"TRB", "CAR"}
		for i, board := range boards {
			id := fmt.Sprintf("fresh_item%d", i)
			err := queue.AddToQueue(id, 1000+i*100, board, "/path/to/photo")
			assert.NoError(t, err)
		}

		// Test that GetNextBatch only returns items for the specified board
		batch, err := queue.GetNextBatch("TRB", 10)
		assert.NoError(t, err)
		assert.Len(t, batch, 1)
		assert.Equal(t, "TRB", batch[0].Src)

		batch, err = queue.GetNextBatch("CAR", 10)
		assert.NoError(t, err)
		assert.Len(t, batch, 1)
		assert.Equal(t, "CAR", batch[0].Src)

		// Test non-existent board
		batch, err = queue.GetNextBatch("NONEXISTENT", 10)
		assert.NoError(t, err)
		assert.Nil(t, batch)
	})

	t.Run("GetNext_DifferentBoards", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add fresh items for different boards
		boards := []string{"BRE", "DDF"}
		for i, board := range boards {
			id := fmt.Sprintf("next_item%d", i)
			err := queue.AddToQueue(id, 1000+i*100, board, "/path/to/photo")
			assert.NoError(t, err)
		}

		// Test that GetNext only returns items for the specified board
		item, err := queue.GetNext("BRE")
		assert.NoError(t, err)
		assert.NotNil(t, item)
		assert.Equal(t, "BRE", item.Src)

		item, err = queue.GetNext("DDF")
		assert.NoError(t, err)
		assert.NotNil(t, item)
		assert.Equal(t, "DDF", item.Src)

		// Test non-existent board
		item, err = queue.GetNext("NONEXISTENT")
		assert.NoError(t, err)
		assert.Nil(t, item)
	})

	t.Run("RemoveBatchFromQueue_EmptyBatch", func(t *testing.T) {
		// Test removing empty batch
		err := queue.RemoveBatchFromQueue([]QueueItem{})
		assert.NoError(t, err)
	})

	t.Run("RemoveBatchFromQueue_MultipleItems", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add multiple items
		items := []QueueItem{}
		for i := 0; i < 5; i++ {
			id := fmt.Sprintf("batch_item_%d", i)
			err := queue.AddToQueue(id, 1000, "TRB", "/path/to/photo")
			assert.NoError(t, err)
			items = append(items, QueueItem{ID: id, Src: "TRB"})
		}

		// Verify items were added
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(5), count)

		// Remove batch
		err = queue.RemoveBatchFromQueue(items)
		assert.NoError(t, err)

		// Verify all items were removed
		count, err = testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	t.Run("AddToQueue_UpdateExisting", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add item
		err := queue.AddToQueue("update_test", 1000, "TRB", "/path/to/photo")
		assert.NoError(t, err)

		// Update same item with different priority
		err = queue.AddToQueue("update_test", 2000, "TRB", "/path/to/photo")
		assert.NoError(t, err)

		// Verify only one item exists with updated values
		count, err := testCol.CountDocuments(ctx, bson.M{})
		require.NoError(t, err)
		assert.Equal(t, int64(1), count)

		var result QueueItem
		err = testCol.FindOne(ctx, bson.M{"_id": "update_test"}).Decode(&result)
		assert.NoError(t, err)
		assert.Equal(t, 2000, result.Priority)
	})

	t.Run("GetNextBatch_PriorityOrdering", func(t *testing.T) {
		ctx := context.Background()
		// Clean up first
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add items with different priorities
		priorities := []int{100, 500, 300, 800, 200}
		for i, priority := range priorities {
			id := fmt.Sprintf("priority_test_%d", i)
			err := queue.AddToQueue(id, priority, "TRB", "/path/to/photo")
			assert.NoError(t, err)
		}

		// Get batch and verify priority ordering (highest first)
		batch, err := queue.GetNextBatch("TRB", 5)
		assert.NoError(t, err)
		assert.Len(t, batch, 5)

		// Verify descending priority order
		for i := 1; i < len(batch); i++ {
			assert.GreaterOrEqual(t, batch[i-1].Priority, batch[i].Priority,
				"Batch should be ordered by priority (highest first)")
		}
	})

	t.Run("GetNextBatch_BugFix_PriorityAndBatchSize", func(t *testing.T) {
		// This test specifically validates the bug fix for GetNextBatch
		// Bug: Previously returned more items than batchSize and didn't properly sort by priority
		ctx := context.Background()
		if _, err := testCol.DeleteMany(ctx, bson.M{}); err != nil {
			t.Errorf("Failed to cleanup test collection: %v", err)
		}

		// Add 10 items with different priorities
		testItems := []struct {
			id       string
			priority int
		}{
			{"item_1", 100},
			{"item_2", 500},
			{"item_3", 200},
			{"item_4", 800},
			{"item_5", 150},
			{"item_6", 600},
			{"item_7", 300},
			{"item_8", 900},
			{"item_9", 250},
			{"item_10", 400},
		}

		for _, item := range testItems {
			err := queue.AddToQueue(item.id, item.priority, "TRB", "/path/to/photo")
			require.NoError(t, err)
		}

		// Test 1: Request batch of 3 - should get exactly 3 highest priority items
		batch1, err := queue.GetNextBatch("TRB", 3)
		assert.NoError(t, err)
		assert.Len(t, batch1, 3, "Should return exactly 3 items when batchSize=3")

		// Verify we got the 3 highest priority items in descending order
		expectedPriorities := []int{900, 800, 600} // Top 3 priorities
		for i, item := range batch1 {
			assert.Equal(t, expectedPriorities[i], item.Priority,
				"Item %d should have priority %d, got %d", i, expectedPriorities[i], item.Priority)
		}

		// Test 2: Request batch of 5 - should get exactly 5 items (next highest)
		batch2, err := queue.GetNextBatch("TRB", 5)
		assert.NoError(t, err)
		assert.Len(t, batch2, 5, "Should return exactly 5 items when batchSize=5")

		// Verify priorities are in descending order
		for i := 1; i < len(batch2); i++ {
			assert.GreaterOrEqual(t, batch2[i-1].Priority, batch2[i].Priority,
				"Batch should be ordered by priority (highest first)")
		}

		// Test 3: Request more items than available - should get remaining items
		batch3, err := queue.GetNextBatch("TRB", 10)
		assert.NoError(t, err)
		assert.Len(t, batch3, 2, "Should return only 2 remaining items")

		// Test 4: No more items available
		batch4, err := queue.GetNextBatch("TRB", 5)
		assert.NoError(t, err)
		assert.Nil(t, batch4, "Should return nil when no items available")
	})
}
